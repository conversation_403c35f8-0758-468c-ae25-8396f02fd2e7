{"name": "opensaas", "type": "module", "dependencies": {"@aws-sdk/client-s3": "^3.523.0", "@aws-sdk/s3-request-presigner": "^3.523.0", "@faker-js/faker": "8.3.1", "@google-analytics/data": "4.1.0", "@google/generative-ai": "^0.24.0", "@headlessui/react": "1.7.13", "@lemonsqueezy/lemonsqueezy.js": "^3.2.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.7", "@types/cheerio": "^0.22.35", "@types/multer": "^1.4.12", "@types/pdfkit": "^0.14.0", "apexcharts": "3.41.0", "cheerio": "^1.1.0", "clsx": "^2.1.0", "epub": "^1.3.0", "file-type": "^20.4.1", "headlessui": "^0.0.0", "jsdom": "^26.1.0", "jszip": "^3.10.1", "multer": "^1.4.5-lts.2", "node-fetch": "3.3.0", "openai": "^4.55.3", "pdfjs-dist": "^5.0.375", "pdfkit": "^0.17.1", "prettier": "3.1.1", "prettier-plugin-tailwindcss": "0.5.11", "react": "^18.2.0", "react-apexcharts": "1.4.1", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "4.11.0", "react-router-dom": "^6.26.2", "stripe": "11.15.0", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.2.7", "vanilla-cookieconsent": "^3.0.1", "vaul": "^1.1.2", "wasp": "file:.wasp/out/sdk/wasp", "zod": "^3.23.8"}, "devDependencies": {"@types/express": "^4.17.13", "@types/jsdom": "^21.1.7", "@types/react": "^18.0.37", "prisma": "5.19.1", "typescript": "^5.1.0", "vite": "^4.3.9"}}