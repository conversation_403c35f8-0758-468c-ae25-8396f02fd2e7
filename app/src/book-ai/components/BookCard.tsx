import type { BookStatus } from "../../shared/types";
import { cn } from "../../client/cn";
import SideDrawer from "./SideDrawer";
import { File } from "wasp/entities";

interface BookCardProps {
  name: string;
  type: string;
  status: BookStatus;
  createdAt: Date;
  file: File;
  onDelete: (id: string) => Promise<void>;
}

export default function BookCard({
  name,
  type,
  status,
  createdAt,
  file,
  onDelete,
}: BookCardProps) {
  const statusColors = {
    COMPLETED: "bg-green-500",
    PROCESSING: "bg-yellow-500",
    FAILED: "bg-red-500",
    PENDING: "bg-blue-500",
  };

  // Processing card state (keep as is)
  if (status === "PROCESSING") {
    return (
      <div className="relative h-[320px] w-[200px] transform overflow-hidden rounded-lg transition-transform duration-300 hover:-translate-y-2">
        {/* Skeleton background */}
        <div className="h-full w-full animate-pulse bg-muted" />

        {/* Shimmer effect */}
        <div className="absolute inset-0">
          <div className="animate-shimmer absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/10 to-transparent" />
        </div>

        {/* Skeleton content */}
        <div className="absolute inset-0 flex flex-col justify-between p-4">
          {/* Status badge - visible and styled */}
          <div className="flex justify-end">
            <div className="flex items-center gap-2 rounded-full bg-yellow-500/20 px-3 py-1">
              <div className="h-2 w-2 rounded-full bg-yellow-500 animate-pulse" />
              <span className="text-xs font-medium text-yellow-500">
                PROCESSING
              </span>
            </div>
          </div>

          {/* Bottom content with visible information */}
          <div>
            <h3 className="text-lg font-bold text-gray-900 dark:text-white">
              {name.split("_")[0]}
            </h3>
            <p className="text-sm text-gray-700 dark:text-gray-200">
              {type.split("/")[1]}
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-300">
              Added {new Date(createdAt).toLocaleDateString()}
            </p>
            <div className="mt-3 flex gap-2">
              <div className="h-8 flex-1 rounded bg-muted-foreground/20" />
              <div className="h-8 w-20 rounded bg-muted-foreground/20" />{" "}
              {/* Adjusted width for "Details" */}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Failed card state
  if (status === "FAILED") {
    return (
      <div className="group relative h-[320px] w-[200px] transform transition-transform duration-300 hover:-translate-y-2">
        <div className="absolute inset-0 overflow-hidden rounded-lg shadow-lg">
          {/* Grayed out cover image */}
          <img
            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT-sXYE1ZoRZPvjQV_s0xzgNQz2MTgfbtowkQ&s"
            alt={name.split("_")[0]}
            className="h-full w-full object-cover grayscale"
          />

          {/* Status badge */}
          <div className="absolute right-2 top-2 rounded-full bg-red-500 px-3 py-1 text-xs text-white">
            FAILED
          </div>

          {/* Error overlay (always visible) */}
          <div className="absolute inset-0 flex flex-col justify-between bg-black/60 p-4">
            <div className="flex items-center justify-center h-1/2">
              <svg
                className="h-12 w-12 text-red-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-bold text-white">
                {name.split("_")[0]}
              </h3>
              <p className="text-sm text-gray-200">{type.split("/")[1]}</p>
              <p className="text-xs text-gray-300">
                Added {new Date(createdAt).toLocaleDateString()}
              </p>
              <div className="mt-3 flex gap-2">
                <button className="flex-1 rounded bg-white px-3 py-1 text-sm font-medium text-gray-900 hover:bg-gray-100">
                  Retry
                </button>
                <SideDrawer
                  className="rounded bg-gray-800 px-3 py-1 text-sm font-medium text-white hover:bg-gray-700"
                  fileDetails={file}
                  onDelete={onDelete}
                >
                  Details
                </SideDrawer>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Pending card state
  if (status === "PENDING") {
    return (
      <div className="group relative h-[320px] w-[200px] transform transition-transform duration-300 hover:-translate-y-2">
        <div className="absolute inset-0 overflow-hidden rounded-lg shadow-lg">
          {/* Dimmed cover image */}
          <img
            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT-sXYE1ZoRZPvjQV_s0xzgNQz2MTgfbtowkQ&s"
            alt={name.split("_")[0]}
            className="h-full w-full object-cover opacity-50"
          />

          {/* Status badge */}
          <div className="absolute right-2 top-2 rounded-full bg-blue-500 px-3 py-1 text-xs text-white">
            PENDING
          </div>

          {/* Info overlay (always visible) */}
          <div className="absolute inset-0 flex flex-col justify-between bg-gradient-to-t from-black/70 to-transparent p-4">
            <div />
            <div>
              <h3 className="text-lg font-bold text-white">
                {name.split("_")[0]}
              </h3>
              <p className="text-sm text-gray-200">{type.split("/")[1]}</p>
              <p className="text-xs text-gray-300">
                Added {new Date(createdAt).toLocaleDateString()}
              </p>
              <div className="mt-3 flex gap-2">
                <button className="flex-1 rounded bg-white/50 px-3 py-1 text-sm font-medium text-gray-900 cursor-not-allowed">
                  Read
                </button>
                <SideDrawer
                  className="rounded bg-gray-800 px-3 py-1 text-sm font-medium text-white hover:bg-gray-700"
                  fileDetails={file}
                  onDelete={onDelete}
                >
                  Details
                </SideDrawer>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Completed card state (keep as is)
  return (
    <div className="group relative h-[320px] w-[200px] transform transition-transform duration-300 hover:-translate-y-2">
      {/* Book Card */}
      <div className="absolute inset-0 overflow-hidden rounded-lg shadow-lg">
        {/* Cover image */}
        <img
          src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT-sXYE1ZoRZPvjQV_s0xzgNQz2MTgfbtowkQ&s"
          alt={name.split("_")[0]}
          className="h-full w-full object-cover"
        />

        {/* Status badge */}
        <div
          className={cn(
            "absolute right-2 top-2 rounded-full px-3 py-1 text-xs text-white",
            statusColors[status]
          )}
        >
          {status}
        </div>

        {/* Book info overlay (visible on hover) */}
        <div className="absolute inset-0 flex flex-col justify-end bg-gradient-to-t from-black/70 to-transparent p-4 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
          <h3 className="text-lg font-bold text-white">{name.split("_")[0]}</h3>
          <p className="text-sm text-gray-200">{type.split("/")[1]}</p>
          <p className="text-xs text-gray-300">
            Added {new Date(createdAt).toLocaleDateString()}
          </p>

          <div className="mt-3 flex gap-2">
            <button className="flex-1 rounded bg-white px-3 py-1 text-sm font-medium text-gray-900 hover:bg-gray-100">
              Read
            </button>
            <SideDrawer
              className="rounded bg-gray-800 px-3 py-1 text-sm font-medium text-white hover:bg-gray-700"
              fileDetails={file}
              onDelete={onDelete}
            >
              Details
            </SideDrawer>
          </div>
        </div>
      </div>
    </div>
  );
}
