import { routes } from "wasp/client/router";
import { useNavigate } from "react-router-dom";

export default function Welcome() {
  const navigate = useNavigate();
  const handleGetStarted = () => {
    navigate(routes.UploadBookRoute.to);
  };
  return (
    // Changed from min-h-screen to h-[calc(100vh-80px)] to account for navbar
    <div className="relative h-[calc(100vh-85px)] flex items-center justify-center bg-white dark:bg-boxdark-2">
      <div className="mx-auto max-w-3xl text-center px-6">
        <h1 className="text-5xl md:text-7xl font-bold text-gray-900 dark:text-white">
          Welcome to Your App
        </h1>

        <p className="mt-8 text-xl md:text-2xl text-gray-600 dark:text-white">
          We're excited to have you here. Let's get you started on your journey.
        </p>

        <div className="mt-12">
          <button
            onClick={handleGetStarted}
            className="inline-block rounded-lg px-8 py-4 text-lg font-semibold text-white bg-yellow-500 hover:bg-yellow-600 transition-colors duration-200 shadow-lg"
          >
            Get Started <span aria-hidden="true">→</span>
          </button>
        </div>
      </div>
    </div>
  );
}
