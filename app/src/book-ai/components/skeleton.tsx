import { cn } from "../../client/cn";

interface BookCardSkeletonProps {
  count?: number;
  className?: string;
}

// Main skeleton component that can render multiple cards
export default function BookCardSkeleton({
  count = 6,
  className,
}: BookCardSkeletonProps) {
  return (
    <div className={cn(className)}>
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={`skeleton-${index}`}
          className="relative h-[320px] w-[200px] transform overflow-hidden rounded-lg shadow-lg"
        >
          {/* Main skeleton background with shimmer */}
          <div className="relative h-full w-full bg-gray-200 dark:bg-gray-800 animate-pulse">
            {/* Shimmer effect */}
            <div className="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 dark:via-white/10 to-transparent" />

            {/* Cover image skeleton */}
            <div className="h-full w-full bg-gray-300 dark:bg-gray-700" />
          </div>

          {/* Status badge skeleton */}
          <div className="absolute right-2 top-2">
            <div className="h-6 w-16 rounded-full bg-gray-300 dark:bg-gray-600 animate-pulse" />
          </div>

          {/* Content overlay skeleton */}
          <div className="absolute inset-0 flex flex-col justify-end bg-gradient-to-t from-black/20 to-transparent p-4">
            {/* Title skeleton */}
            <div className="mb-2">
              <div className="h-5 w-3/4 rounded bg-gray-400/50 dark:bg-gray-500/50 animate-pulse mb-1" />
            </div>

            {/* Type skeleton */}
            <div className="mb-1">
              <div className="h-4 w-1/2 rounded bg-gray-400/40 dark:bg-gray-500/40 animate-pulse" />
            </div>

            {/* Date skeleton */}
            <div className="mb-3">
              <div className="h-3 w-2/3 rounded bg-gray-400/30 dark:bg-gray-500/30 animate-pulse" />
            </div>

            {/* Buttons skeleton */}
            <div className="flex gap-2">
              <div className="flex-1 h-8 rounded bg-gray-400/40 dark:bg-gray-500/40 animate-pulse" />
              <div className="h-8 w-20 rounded bg-gray-400/40 dark:bg-gray-500/40 animate-pulse" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
