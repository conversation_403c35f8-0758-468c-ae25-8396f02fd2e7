import { useAuth } from "wasp/client/auth";
import { useEffect, useState } from "react";
import { api } from "wasp/client/api";
import Welcome from "../components/Welcome";
import BookListPage from "./BookListPage";

export default function GetStartedPage() {
  const { data: user } = useAuth();

  const [loading, setLoading] = useState(true);
  const [hasFiles, setHasFiles] = useState(false);

  useEffect(() => {
    async function checkUserFiles() {
      const response = await api.get("http://localhost:3001/api/checkFiles");

      setLoading(false);

      if (response.data.hasFiles) setHasFiles(true);
    }

    checkUserFiles();
  }, []);

  return <div>{loading ? "" : hasFiles ? <BookListPage /> : <Welcome />}</div>;
}
