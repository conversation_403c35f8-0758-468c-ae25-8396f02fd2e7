import EPub from "epub";
import * as cheerio from "cheerio";

// Define interfaces for better type safety
interface ChapterStructure {
  elements: ElementInfo[];
  styles: string[];
  images: ImageElement[];
  links: LinkElement[];
}

interface ElementInfo {
  tag: string;
  text: string;
  html: string | null;
  attributes: Record<string, string>;
  classes: string;
  id: string;
  type: ElementType;
  level?: number;
  src?: string;
  href?: string;
  listType?: string;
}

type ElementType =
  | "heading"
  | "paragraph"
  | "image"
  | "link"
  | "quote"
  | "list"
  | "listItem"
  | "other";

interface ImageElement extends ElementInfo {
  type: "image";
  src: string;
}

interface LinkElement extends ElementInfo {
  type: "link";
  href: string;
}

interface Chapter {
  id: string;
  href: string;
  title: string;
  order: number;
  originalHtml: string;
  structure: ChapterStructure;
  textContent: string;
}

interface BookData {
  metadata: {
    title: string;
    creator: string;
    language: string;
    description: string;
    date: string;
    identifier: string;
    original: Record<string, any>;
  };
  spine: {
    id: string;
    href: string;
    title: string;
    order: number;
  }[];
  manifest: Record<string, any>;
  chapters: Chapter[];
}

interface FlowItem {
  id: string;
  href: string;
  title?: string;
}

export default class EpubExtractor {
  private extractedData: BookData | null = null;

  constructor() {
    this.extractedData = null;
  }

  async extractWithStructure(epubPath: string): Promise<BookData> {
    return new Promise<BookData>((resolve, reject) => {
      const epub = new EPub(epubPath);

      epub.on("end", async () => {
        try {
          // Get all metadata and structure info
          const bookData: BookData = {
            metadata: {
              title: epub.metadata.title || "Unknown Title",
              creator: epub.metadata.creator || "Unknown Author",
              language: epub.metadata.language || "en",
              description: epub.metadata.description || "",
              date: epub.metadata.date || new Date().toISOString(),
              identifier: epub.metadata.creatorFileAs || "",
              // Keep all original metadata for reconstruction
              original: epub.metadata || {},
            },

            // Preserve spine order (reading sequence)
            spine: epub.flow.map((item: FlowItem, index: number) => ({
              id: item.id,
              href: item.href,
              title: item.title || `Chapter ${index + 1}`,
              order: index,
            })),

            // Store manifest for file relationships
            manifest: epub.manifest || {},

            // Extract chapters with structure preserved
            chapters: [],
          };

          // Process chapters in parallel for performance
          const chapterPromises = epub.flow.map(
            (flowItem: FlowItem, index: number) => {
              return new Promise<Chapter | null>((resolveChapter) => {
                epub.getChapter(
                  flowItem.id,
                  (error: Error | null, rawHtml: string) => {
                    if (error) {
                      console.error(
                        `Error reading chapter ${flowItem.id}:`,
                        error
                      );
                      resolveChapter(null);
                      return;
                    }

                    // Parse and structure the content
                    const structuredContent = this.parseChapterStructure(
                      rawHtml,
                      flowItem
                    );

                    resolveChapter({
                      id: flowItem.id,
                      href: flowItem.href,
                      title: flowItem.title || `Chapter ${index + 1}`,
                      order: index,

                      // Keep original HTML for reconstruction
                      originalHtml: rawHtml,

                      // Structured content for processing
                      structure: structuredContent,

                      // Clean text for paraphrasing
                      textContent: this.extractCleanText(structuredContent),
                    });
                  }
                );
              });
            }
          );

          // Wait for all chapters to be processed
          const chapters = await Promise.all(chapterPromises);
          bookData.chapters = chapters
            .filter((ch): ch is Chapter => ch !== null)
            .sort((a, b) => a.order - b.order);

          this.extractedData = bookData;
          resolve(bookData);
        } catch (error) {
          reject(error);
        }
      });

      epub.on("error", reject);
      epub.parse();
    });
  }

  // Parse chapter while preserving structure
  parseChapterStructure(html: string, flowItem: FlowItem): ChapterStructure {
    const $ = cheerio.load(html);
    const structure: ChapterStructure = {
      elements: [],
      styles: [],
      images: [],
      links: [],
    };

    // Extract styles
    $("style").each((i, elem) => {
      const styleContent = $(elem).html();
      if (styleContent) {
        structure.styles.push(styleContent);
      }
    });

    // Process body content while preserving hierarchy
    $("body")
      .find("*")
      .each((i, elem) => {
        const $elem = $(elem);
        // Fix: Check if the element has a tagName property
        const tagName =
          elem.type === "tag" && "name" in elem ? elem.name.toLowerCase() : "";

        // Skip elements without a tag name or empty elements
        if (
          !tagName ||
          (!$elem.text().trim() && !["img", "br", "hr"].includes(tagName))
        ) {
          return;
        }

        const element: ElementInfo = {
          tag: tagName,
          text: $elem.text().trim(),
          html: $elem.html(),
          // Fix: Safely access attribs property
          attributes:
            elem.type === "tag" && "attribs" in elem ? elem.attribs : {},
          classes: $elem.attr("class") || "",
          id: $elem.attr("id") || "",
          type: "other",
        };

        // Special handling for different element types
        switch (tagName) {
          case "h1":
          case "h2":
          case "h3":
          case "h4":
          case "h5":
          case "h6":
            element.type = "heading";
            element.level = parseInt(tagName.charAt(1));
            break;

          case "p":
            element.type = "paragraph";
            break;

          case "img":
            element.type = "image";
            element.src = $elem.attr("src") || "";
            structure.images.push(element as ImageElement);
            break;

          case "a":
            element.type = "link";
            element.href = $elem.attr("href") || "";
            structure.links.push(element as LinkElement);
            break;

          case "blockquote":
            element.type = "quote";
            break;

          case "ul":
          case "ol":
            element.type = "list";
            element.listType = tagName;
            break;

          case "li":
            element.type = "listItem";
            break;

          default:
            element.type = "other";
        }

        structure.elements.push(element);
      });

    return structure;
  }

  // Extract clean text for paraphrasing
  extractCleanText(structure: ChapterStructure): string {
    return structure.elements
      .filter((elem) => elem.text && elem.text.length > 0)
      .map((elem) => elem.text)
      .join(" ")
      .replace(/\s+/g, " ")
      .trim();
  }

  // Get processable chunks with structure context
  getProcessableChunks(maxChunkSize = 2000): Array<{
    chapterId: string;
    chapterTitle: string;
    elements: ElementInfo[];
    textLength: number;
  }> {
    if (!this.extractedData) {
      throw new Error(
        "No extracted data available. Run extractWithStructure first."
      );
    }

    const chunks: Array<{
      chapterId: string;
      chapterTitle: string;
      elements: ElementInfo[];
      textLength: number;
    }> = [];

    this.extractedData.chapters.forEach((chapter) => {
      const chapterChunks = this.splitChapterIntoChunks(chapter, maxChunkSize);
      chunks.push(...chapterChunks);
    });

    return chunks;
  }

  splitChapterIntoChunks(
    chapter: Chapter,
    maxChunkSize: number
  ): Array<{
    chapterId: string;
    chapterTitle: string;
    elements: ElementInfo[];
    textLength: number;
  }> {
    const chunks: Array<{
      chapterId: string;
      chapterTitle: string;
      elements: ElementInfo[];
      textLength: number;
    }> = [];

    let currentChunk = {
      chapterId: chapter.id,
      chapterTitle: chapter.title,
      elements: [] as ElementInfo[],
      textLength: 0,
    };

    chapter.structure.elements.forEach((element) => {
      const elementTextLength = element.text ? element.text.length : 0;

      if (
        currentChunk.textLength + elementTextLength > maxChunkSize &&
        currentChunk.elements.length > 0
      ) {
        // Save current chunk and start new one
        chunks.push(currentChunk);
        currentChunk = {
          chapterId: chapter.id,
          chapterTitle: chapter.title,
          elements: [element],
          textLength: elementTextLength,
        };
      } else {
        currentChunk.elements.push(element);
        currentChunk.textLength += elementTextLength;
      }
    });

    // Add the last chunk if it has content
    if (currentChunk.elements.length > 0) {
      chunks.push(currentChunk);
    }

    return chunks;
  }
}

export {
  EpubExtractor,
  type BookData,
  type Chapter,
  type ChapterStructure,
  type ElementInfo,
};
