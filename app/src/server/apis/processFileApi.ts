import { Prisma, PrismaClient } from "@prisma/client";
import { ProcessFile } from "wasp/server/api";
import { MiddlewareConfigFn } from "wasp/server";
import fs from "fs/promises";
import path from "path";
import EPub from "epub";
import { fileTypeFromFile } from "file-type";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { broadcastFileStatusUpdate } from "../utils/websocketUtils";
import JSZip from "jszip";
import * as cheerio from "cheerio";

const prisma = new PrismaClient();
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "");
const OUTPUT_DIR = "/tmp/tp-processed";

// Ensure output directory exists
async function ensureOutputDirExists() {
  try {
    await fs.mkdir(OUTPUT_DIR, { recursive: true });
  } catch (error) {
    console.error("Error creating output directory:", error);
  }
}

// Define tone-specific prompts
const TONE_PROMPTS = {
  academic: `Rewrite this text in an academic tone. Use formal language, precise terminology, and scholarly structure. Maintain objectivity and include appropriate academic phrasing. Present information systematically and analytically:`,
  simplified: `Rewrite this text in simple, clear language. Use short sentences, common words, and straightforward explanations. Break down complex concepts into easy-to-understand parts:`,
  kindergarten: `Rewrite this text for young children (ages 5-6). Use very simple words, short sentences, and fun examples. Explain things like you're talking to a curious child:`,
  poetic: `Rewrite this text in a poetic style. Use beautiful, flowing language with rhythm and imagery. Include metaphors, descriptive phrases, and lyrical expressions while keeping the meaning clear:`,
  humorous: `Rewrite this text with humor and wit. Add funny observations, clever wordplay, and amusing analogies. Keep it entertaining while preserving the important information:`,
  philosophical: `Rewrite this text in a philosophical tone. Explore deeper meanings, ask thought-provoking questions, and connect ideas to broader concepts about life, existence, and human nature:`,
  sarcastic: `Rewrite this text with a sarcastic and witty tone. Use irony, clever remarks, and dry humor. Be entertaining and slightly cynical while still conveying the essential information:`,
};

export const apiMiddleware: MiddlewareConfigFn = (config) => {
  return config;
};

export const processFile: ProcessFile = async (req, res, context) => {
  try {
    if (!context.user) {
      return res.status(401).json({
        error: "Authentication required",
      });
    }

    if (!req.body) {
      return res.status(400).json({
        error: "No file uploaded",
      });
    }

    const file = await context.entities.File.findUnique({
      where: {
        id: req.body.fileId,
        userId: context.user.id,
      },
    });

    if (!file || file.status !== "PENDING") {
      return res.status(400).json({
        error: "Invalid file",
      });
    }

    await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
      const updatedFile = await tx.file.update({
        where: {
          id: req.body.fileId,
        },
        data: { status: "PROCESSING" },
        select: {
          id: true,
          status: true,
        },
      });

      await tx.user.update({
        where: {
          id: context.user!.id,
        },
        data: { credits: { decrement: req.body.requiredCredits } },
      });

      broadcastFileStatusUpdate(context.user!.id, updatedFile);
    });

    // Start processing in the background
    processFileTask(
      context.entities.File,
      req.body.path,
      req.body.fileId,
      req.body.tone || "simplified"
    );

    return res.status(200).json({ message: "Processing started" });
  } catch (error) {
    return res.status(500).json({ message: (error as Error).message });
  }
};

async function processFileTask(
  fileModel: Prisma.FileDelegate,
  filePath: string,
  id: string,
  tone: string
) {
  try {
    console.log("Processing file:", filePath);

    await ensureOutputDirExists();

    // Determine file type
    const fileType = await fileTypeFromFile(filePath);

    if (fileType?.mime !== "application/epub+zip") {
      throw new Error("Only EPUB files are supported");
    }

    // Process EPUB while preserving structure
    const epub: EPub = await parseEpub(filePath);

    // Print available chapters
    epub.flow.forEach((chapter, index) => {
      console.log(`${index + 1}. (ID: ${chapter.id})`);
    });

    const chapters = await extractAllChapters(epub, filePath);

    const processedChunks = await Promise.all(
      chapters.map(
        async (ch) => await paraphraseTextWithGemini(ch.chunks, tone)
      )
    );

    structureResults(chapters, processedChunks);

    console.log(`Processed content saved `);

    await createEpub(chapters, filePath, epub);

    // Update the file record
    const updatedFile = await fileModel.update({
      where: { id },
      data: {
        status: "COMPLETED",
      },
      select: {
        id: true,
        status: true,
        userId: true,
      },
    });

    broadcastFileStatusUpdate(updatedFile.userId, {
      id: updatedFile.id,
      status: updatedFile.status,
    });

    console.log(`File ${id} processed successfully`);
  } catch (error) {
    console.error("Error processing file:", (error as Error).message);

    const updatedFile = await fileModel.update({
      where: { id },
      data: {
        status: "FAILED",
      },
      select: {
        id: true,
        status: true,
        userId: true,
      },
    });

    broadcastFileStatusUpdate(updatedFile.userId, {
      id: updatedFile.id,
      status: updatedFile.status,
    });
  }
}

function parseEpub(epubPath: string): Promise<EPub> {
  return new Promise<EPub>((resolve, reject) => {
    const epub = new EPub(epubPath);

    epub.on("end", () => {
      console.log("✅ EPUB parsed successfully");
      console.log("📚 Title:", epub.metadata.title);
      console.log("👤 Author:", epub.metadata.creator);
      console.log("📄 Total chapters:", epub.flow.length);

      resolve(epub);
    });

    epub.on("error", (error) => {
      console.error("❌ Error parsing EPUB:", error);
      reject(error);
    });

    console.log("📖 Loading EPUB file...");
    epub.parse();
  });
}

// Function to get raw chapter content
async function getChapterContent(zip: JSZip, href: string) {
  const file = zip.file(href);
  if (!file) {
    throw new Error(`File not found in ZIP: ${href}`);
  }
  return await file.async("string");
}

interface Chapter {
  html: string;
  dom?: any;
  title: string;
  order: number;
  chunks: string[][];
  root: { $: any; nodes: any[] };
}

// Function to extract all chapters
async function extractAllChapters(
  epub: EPub,
  filePath: string
): Promise<Chapter[]> {
  console.log("📄 Extracting chapter contents...");

  const file = await fs.readFile(filePath);
  const zip = await JSZip.loadAsync(file);

  const chapters = await Promise.all(
    epub.flow.map(async (chapter, i) => {
      console.log(
        `📖 Processing chapter ${i + 1}/${epub.flow.length}: ${chapter.title}`
      );

      const html = await getChapterContent(zip, chapter.href);
      const root = extractText(html);
      const chunks = await chunkText(root.nodes);

      if (html) {
        return {
          html: html as string,
          title: chapter.title,
          order: i + 1,
          chunks: chunks,
          root: root,
        };
      } else {
        console.log(`   ❌ Failed to extract chapter`);
        return null;
      }
    })
  );

  return chapters.filter((chapter) => chapter !== null);
}

function extractText(htmlContent: any) {
  const $ = cheerio.load(htmlContent, { xmlMode: true });
  const nodes: any[] = [];

  function walk(node: any) {
    $(node)
      .contents()
      .each((_, child) => {
        if (child.type === "text") {
          const text = $(child).text().trim();
          if (text) {
            nodes.push({ node: child, original: text });
          }
        } else if (child.type === "tag") {
          walk(child);
        }
      });
  }

  walk($("body"));

  return { $, nodes };
}

async function chunkText(
  textElements: any[],
  maxTokens = 50000
): Promise<string[][]> {
  const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash" });
  const chunks: string[][] = [];
  let currentChunkList: string[] = [];
  let currentChunk = "";

  for (const element of textElements) {
    // Create test chunk by adding this element's text
    const testChunk = currentChunk + element.original;

    // Check if adding this element would exceed the token limit
    const { totalTokens } = await model.countTokens(testChunk);

    if (totalTokens > maxTokens && currentChunk.length > 0) {
      // Current chunk is full, save it and start a new one
      chunks.push([...currentChunkList]);
      currentChunk = element.original;
      currentChunkList = [element.original];
    } else {
      // Add element original to current chunk
      currentChunkList.push(element.original);
      currentChunk = testChunk;
    }
  }

  // Add the last chunk if it's not empty
  if (currentChunkList.length > 0) {
    chunks.push(currentChunkList);
  }

  return chunks;
}

// Process text with Gemini using tone-specific prompts
async function paraphraseTextWithGemini(
  chunks: string[][],
  tone: string = "simplified"
): Promise<any[]> {
  const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash" });
  const tonePrompt =
    TONE_PROMPTS[tone as keyof typeof TONE_PROMPTS] || TONE_PROMPTS.simplified;

  const paraphrasedChunks: string[][] = [];

  for (const chunk of chunks) {
    try {
      const fullPrompt = `${tonePrompt}

You will receive a JSON array of text strings. Your task is to rewrite each string individually while keeping the exact same array structure.

INPUT ARRAY:
${JSON.stringify(chunk, null, 2)}

RULES:
1. Rewrite each array element separately (do not combine them)
2. Keep the same number of elements in the array
3. Each element should be the rewritten version of the corresponding input element
4. Return only a valid JSON array
5. Do not merge multiple elements into one

EXAMPLE:
Input: ["First paragraph text", "Second paragraph text", "Third paragraph text"]
Output: ["Rewritten first paragraph", "Rewritten second paragraph", "Rewritten third paragraph"]

Return the rewritten JSON array:`;

      const result = await model.generateContent(fullPrompt);
      let response = result.response.text().trim();

      // Remove potential markdown code blocks
      response = response
        .replace(/^```(?:json)?\s*/, "")
        .replace(/\s*```$/, "");

      // Find JSON array in response
      const jsonMatch = response.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        response = jsonMatch[0];
      }

      try {
        const parsedArray = JSON.parse(response);

        // Validate the structure
        if (Array.isArray(parsedArray) && parsedArray.length === chunk.length) {
          paraphrasedChunks.push(parsedArray);
          console.log(
            `✅ Successfully paraphrased chunk with ${parsedArray.length} elements`
          );
        } else {
          console.warn(
            `❌ Structure mismatch: expected ${chunk.length} elements, got ${parsedArray.length}`
          );
          paraphrasedChunks.push(chunk); // Use original
        }
      } catch (parseError) {
        console.error("❌ Failed to parse JSON, using original chunk");
        console.log("Raw response:", response.substring(0, 200) + "...");
        paraphrasedChunks.push(chunk);
      }

      // Add delay to avoid rate limiting
      await new Promise((resolve) => setTimeout(resolve, 300));
    } catch (error) {
      console.error(`❌ Error paraphrasing chunk:`, error);
      paraphrasedChunks.push(chunk);
    }
  }

  return paraphrasedChunks;
}

function structureResults(chapters: any[], paraphrasedChunks: any[]) {
  chapters.forEach((ch, i) => {
    if (paraphrasedChunks[i].length === 0) return;

    const chunks = paraphrasedChunks[i].flat();
    ch.root.nodes.forEach(({ node }: { node: any }, y: number) => {
      const text = chunks[y];
      if (text !== undefined) {
        node.data = text;
      }
    });
  });
}

async function createEpub(chapters: Chapter[], filePath: string, epub: EPub) {
  const file = await fs.readFile(filePath);
  const originalZip = await JSZip.loadAsync(file);

  const updatedChapters: { [key: string]: string } = {};
  epub.flow.forEach(
    (chapter, i) => (updatedChapters[chapter.href] = chapters[i].root.$.html())
  );

  for (const [href, newContent] of Object.entries(updatedChapters)) {
    if (originalZip.file(href)) {
      originalZip.file(href, newContent as string);
      console.log(`✅ Replaced: ${href}`);
    } else {
      console.warn(`⚠️ File not found in original EPUB: ${href}`);
    }
  }

  const newZipBuffer = await originalZip.generateAsync({
    type: "nodebuffer",
    mimeType: "application/epub+zip",
    compression: "DEFLATE",
    compressionOptions: { level: 9 },
    streamFiles: true,
  });

  const outputName = `${path.basename(
    filePath,
    path.extname(filePath)
  )}-updated-${Date.now()}.epub`;
  const outputPath = path.join(OUTPUT_DIR, outputName);
  await fs.writeFile(outputPath, newZipBuffer);

  console.log(`📘 New EPUB created at: ${outputPath}`);
}
