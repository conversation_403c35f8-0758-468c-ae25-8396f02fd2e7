import {
  type WebSocketDefinition,
  WaspSocketData,
} from "wasp/server/webSocket";
import type { Server } from "socket.io";

// Global variable to store the Socket.IO server instance for broadcasting
let globalSocketServer: Server | null = null;

export const fileStatusUpdates: WebSocketDefinition = (io, context) => {
  // Store the server instance globally for broadcasting
  if (!globalSocketServer) {
    globalSocketServer = io;
  }

  io.on("connection", (socket) => {
    console.log("WebSocket connection attempt");
    console.log("Socket data:", socket.data);

    // Store user ID to track which files to monitor
    if (!context.entities) {
      console.error("WebSocket context missing entities");
      socket.disconnect();
      return;
    }

    // Get user ID from socket data (Wasp automatically adds user to socket.data when JWT is valid)
    const userId = socket.data.user?.id;

    if (!userId) {
      console.error(
        "WebSocket connection missing user ID - user may not be authenticated"
      );
      console.error(
        "Available socket data:",
        JSON.stringify(socket.data, null, 2)
      );
      console.error("Socket handshake auth:", socket.handshake.auth);
      console.error("Socket handshake headers:", socket.handshake.headers);
      socket.disconnect();
      return;
    }

    console.log(`User ${userId} connected to file status updates`);

    // Add user ID to socket data for identification in broadcasts
    (socket.data as SocketData).userId = userId;

    // Handle disconnect
    socket.on("disconnect", () => {
      console.log(`User ${userId} disconnected from file status updates`);
    });
  });
};

// Export function to get the global socket server for broadcasting
export function getSocketServer(): Server | null {
  return globalSocketServer;
}

// Data that is attached to the socket.
interface SocketData extends WaspSocketData {
  userId?: string;
}
