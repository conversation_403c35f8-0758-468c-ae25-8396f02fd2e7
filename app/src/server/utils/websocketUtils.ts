import { getSocketServer } from "../websockets/fileStatusUpdates";

export function broadcastFileStatusUpdate(
  userId: string,
  fileData: { id: string; status: string }
) {
  const io = getSocketServer();

  if (!io) {
    console.warn("WebSocket server not available for broadcasting updates");
    return;
  }

  // Broadcast to all connected sockets, filtering by userId
  io.sockets.sockets.forEach((socket) => {
    if (socket.data && (socket.data as any).userId === userId) {
      socket.emit("message", {
        type: "FILE_STATUS_UPDATE",
        file: fileData,
      });
    }
  });

  console.log(`Broadcasted file status update for user ${userId}:`, fileData);
}
